'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import styled from 'styled-components';
import { appTheme } from '@/app/theme';
import ChatSidebar from './components/ChatSidebar';
import ChatMain from './components/ChatMain';
import CreateChatModal from './components/CreateChatModal';
import ChatDiagnostic from './components/ChatDiagnostic';
import { chatApi, chatMessageApi, dataApi } from '@/services/chatService';
import { createTaskChat } from '@/services/taskChatHelper';
import { createPrivateChat } from '@/services/privateChatHelper';

import { useSocket } from '@/lib/socket-context';
import { useSocketEvent, useSocketEmit } from '@/hooks/useSocket';

type RoomType = 'private' | 'task' | 'department' | 'organization';

interface Room {
  id: string;
  name: string;
  type: RoomType;
  lastMessage?: string;
  lastMessageTime?: string;
  lastMessageType?: string;
  lastMessageStatus?: string;
  unreadCount?: number;
  isOnline?: boolean;
  avatar?: string;
  isBot?: boolean;
  botDuration?: number;
  chatUsers?: Array<{
    user: {
      id: number;
      firstName: string;
      lastName: string;
    };
  }>;
}

interface Message {
  id: string;
  senderId: string;
  senderName: string;
  content: string;
  timestamp: string;
  type: 'text' | 'image' | 'file' | 'sticker' | 'link';
  status: 'sending' | 'delivered' | 'read' | 'failed';
  user?: {
    id: number;
    firstName: string;
    lastName: string;
    imageUrl?: string;
  };
}

const ChatContainer = styled.div`
  display: flex;
  height: calc(100vh - 100px);
  width: 100%;
  background: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  box-shadow: ${appTheme.shadows.lg};
  overflow: hidden;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;

  /* Desktop (1024px+) - maintain current layout */
  @media (min-width: ${appTheme.breakpoints.lg}) {
    height: calc(100vh - 100px);
    border-radius: ${appTheme.borderRadius.lg};
    box-shadow: ${appTheme.shadows.lg};
    margin: ${appTheme.spacing.md} auto;
  }

  /* Tablet (768px - 1023px) - adjust spacing and sizing */
  @media (max-width: ${appTheme.breakpoints.lg}) and (min-width: ${appTheme.breakpoints.md}) {
    height: calc(100vh - 90px);
    border-radius: ${appTheme.borderRadius.md};
    box-shadow: ${appTheme.shadows.md};
    margin: ${appTheme.spacing.sm} auto;
    max-width: 100%;
  }

  /* Mobile (< 768px) - account for footer menu at bottom */
  @media (max-width: ${appTheme.breakpoints.md}) {
    height: calc(100vh - 80px); /* Account for 80px footer menu */
    border-radius: 0;
    box-shadow: none;
    margin: 0;
    max-width: 100%;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 80px; /* Leave space for footer menu */
  }

  /* Small mobile (< 480px) - account for smaller footer menu */
  @media (max-width: ${appTheme.breakpoints.sm}) {
    height: calc(100vh - 70px); /* Account for 70px footer menu */
    bottom: 70px; /* Leave space for smaller footer menu */
  }
`;

function ChatPageContent() {
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState<RoomType>('private');
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [rooms, setRooms] = useState<Record<RoomType, Room[]>>({
    private: [],
    task: [],
    department: [],
    organization: [],
  });
  const [loading, setLoading] = useState(true);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [loadingMoreMessages, setLoadingMoreMessages] = useState(false);
  const [hasMoreMessages, setHasMoreMessages] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [tabLoading, setTabLoading] = useState<Record<RoomType, boolean>>({
    private: false,
    task: false,
    department: false,
    organization: false,
  });
  const [currentUser, setCurrentUser] = useState<any>(null);

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [createChatType, setCreateChatType] = useState<RoomType>('private');
  const [showDiagnostic, setShowDiagnostic] = useState(false);

  // Mobile sidebar state
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(true);

  // Handle URL params for initial tab
  useEffect(() => {
    const tab = searchParams.get('tab') as RoomType;
    if (tab && ['private', 'task', 'department', 'organization'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // Add keyboard shortcut for diagnostic (Ctrl+Shift+D)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        setShowDiagnostic(true);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Handle task chat creation from session storage
  useEffect(() => {
    const handleTaskChatFromSession = async () => {
      const taskChatData = sessionStorage.getItem('openTaskChat');
      if (taskChatData) {
        try {
          const { taskId, taskTitle } = JSON.parse(taskChatData);
          sessionStorage.removeItem('openTaskChat'); // Clean up

          // Create or find task chat
          const result = await createTaskChat(taskId, taskTitle);

          if (result.success) {
            // Load chats to include the new/existing chat
            await loadChats();

            // Set active tab to task and select the chat
            setActiveTab('task');

            // Find and select the task chat
            setTimeout(() => {
              setRooms(prev => {
                const taskChat = prev.task.find(room => parseInt(room.id) === result.chat.id);
                if (taskChat) {
                  setSelectedRoom(taskChat);
                  // Emit join_chat event to join the socket room
                  emit('join_chat', {
                    chat_id: taskChat.id,
                  });
                }
                return prev;
              });
            }, 100);
          }
        } catch (error) {
          console.error('Failed to handle task chat from session:', error);
        }
      }
    };

    if (currentUser) {
      handleTaskChatFromSession();
    }
  }, [currentUser]);

  // Handle private chat creation from session storage
  useEffect(() => {
    const handlePrivateChatFromSession = async () => {
      const privateChatData = sessionStorage.getItem('openPrivateChat');
      if (privateChatData) {
        try {
          const { currentUserId, targetUserId, targetUserName } = JSON.parse(privateChatData);
          sessionStorage.removeItem('openPrivateChat'); // Clean up

          // Create or find private chat
          const result = await createPrivateChat(currentUserId, targetUserId, targetUserName);

          if (result.success) {
            // Load chats to include the new/existing chat
            await loadChats();

            // Set active tab to private and select the chat
            setActiveTab('private');

            // Find and select the private chat
            setTimeout(() => {
              setRooms(prev => {
                const privateChat = prev.private.find(room => parseInt(room.id) === result.chat.id);
                if (privateChat) {
                  setSelectedRoom(privateChat);
                  // Emit join_chat event to join the socket room
                  emit('join_chat', {
                    chat_id: privateChat.id,
                  });
                }
                return prev;
              });
            }, 100);
          }
        } catch (error) {
          console.error('Failed to handle private chat from session:', error);
        }
      }
    };

    if (currentUser) {
      handlePrivateChatFromSession();
    }
  }, [currentUser]);

  // Load current user and initial chats
  useEffect(() => {
    const loadInitialData = async () => {
      if (!currentUser) {
        setLoading(true);
        try {
          // Load current user
          const userResponse = await dataApi.getCurrentUser();
          setCurrentUser(userResponse.user);
        } catch (error) {
          console.error('Failed to load user data:', error);
          setLoading(false);
          return;
        }
      }

      // Load chats for the active tab
      if (currentUser && (rooms[activeTab].length === 0 || shouldRefreshTab(activeTab))) {
        await loadChatsByType(activeTab);
      }

      setLoading(false);
    };

    loadInitialData();
  }, [currentUser, activeTab]); // Dependencies: currentUser and activeTab

  // Function to load chats from API
  const loadChats = async () => {
    try {
      // Explicitly filter chats by current user to ensure proper membership filtering
      const response = await chatApi.getChats({
        userId: currentUser?.id,
      });
      const chats = response.chats || [];

      // Get unread counts for all chats
      const unreadCountsResponse = await chatMessageApi.getAllUnreadCounts();
      const unreadCountsMap: Record<string, number> = {};

      if (unreadCountsResponse.unreadCounts) {
        unreadCountsResponse.unreadCounts.forEach((item: any) => {
          unreadCountsMap[item.chatId.toString()] = item.unreadCount;
        });
      }

      // Group chats by type
      const groupedChats: Record<RoomType, Room[]> = {
        private: [],
        task: [],
        department: [],
        organization: [],
      };

      chats.forEach((chat: any) => {
        const chatType = chat.chatType.toLowerCase() as RoomType;
        const chatId = chat.id.toString();
        const room: Room = {
          id: chatId,
          name: chat.name || getDefaultChatName(chat, true),
          type: chatType,
          lastMessage: getLastMessagePreview(chat.messages),
          lastMessageTime: getLastMessageTime(chat.messages),
          lastMessageType: getLastMessageType(chat.messages),
          lastMessageStatus: getLastMessageStatus(chat.messages),
          unreadCount: unreadCountsMap[chatId] || 0,
          isOnline: chatType === 'private' ? Math.random() > 0.5 : undefined,
          isBot: chat.isBot || false,
          botDuration: chat.botDuration || undefined,
          chatUsers: chat.chatUsers || [],
        };

        if (groupedChats[chatType]) {
          groupedChats[chatType].push(room);
        }
      });

      setRooms(groupedChats);
    } catch (error) {
      console.error('Failed to load chats:', error);
    }
  };

  // Function to load chats by specific type
  const loadChatsByType = async (chatType: RoomType) => {
    setTabLoading(prev => ({ ...prev, [chatType]: true }));

    try {
      // Explicitly filter chats by current user and chat type
      const response = await chatApi.getChats({
        chatType: chatType.toUpperCase(),
        userId: currentUser?.id,
      });
      const chats = response.chats || [];

      // Get unread counts for all chats
      const unreadCountsResponse = await chatMessageApi.getAllUnreadCounts();
      const unreadCountsMap: Record<string, number> = {};

      if (unreadCountsResponse.unreadCounts) {
        unreadCountsResponse.unreadCounts.forEach((item: any) => {
          unreadCountsMap[item.chatId.toString()] = item.unreadCount;
        });
      }

      // Transform chats to Room format
      const rooms: Room[] = chats.map((chat: any) => {
        const chatId = chat.id.toString();
        return {
          id: chatId,
          name: chat.name || getDefaultChatName(chat, true),
          type: chatType,
          lastMessage: getLastMessagePreview(chat.messages),
          lastMessageTime: getLastMessageTime(chat.messages),
          lastMessageType: getLastMessageType(chat.messages),
          lastMessageStatus: getLastMessageStatus(chat.messages),
          unreadCount: unreadCountsMap[chatId] || 0,
          isOnline: chatType === 'private' ? Math.random() > 0.5 : undefined,
          isBot: chat.isBot || false,
          botDuration: chat.botDuration || undefined,
          chatUsers: chat.chatUsers || [],
        };
      });

      // Update only the specific chat type
      setRooms(prev => ({
        ...prev,
        [chatType]: rooms,
      }));
    } catch (error) {
      console.error(`Failed to load ${chatType} chats:`, error);
    } finally {
      setTabLoading(prev => ({ ...prev, [chatType]: false }));
    }
  };

  // Helper function to get default chat name
  const getDefaultChatName = (chat: any, excludeCurrentUser: boolean = true) => {
    if (chat.chatType === 'PRIVATE') {
      if (excludeCurrentUser && currentUser && chat.chatUsers) {
        // Filter out the current user from the chat name
        const otherUsers = chat.chatUsers.filter((cu: any) => cu.user.id !== currentUser.id);
        return (
          otherUsers.map((cu: any) => `${cu.user.firstName} ${cu.user.lastName}`).join(', ') ||
          'Private Chat'
        );
      }
      return (
        chat.chatUsers?.map((cu: any) => `${cu.user.firstName} ${cu.user.lastName}`).join(', ') ||
        'Private Chat'
      );
    }
    if (chat.task) return chat.task.taskTitle;
    if (chat.department) {
      // New naming convention: "Department Name (Organization Name)"
      const orgName = chat.department.organization?.name || chat.organization?.name;
      return orgName ? `${chat.department.name} (${orgName})` : chat.department.name;
    }
    if (chat.organization) return chat.organization.name;
    return 'Chat';
  };

  // Helper function to get last message preview
  const getLastMessagePreview = (messages: any[]) => {
    if (!messages || messages.length === 0) return '';
    const lastMessage = messages[0]; // Assuming messages are sorted by date desc
    return lastMessage.content;
  };

  const getLastMessageTime = (messages: any[]) => {
    if (!messages || messages.length === 0) return '';
    const lastMessage = messages[0]; // Assuming messages are sorted by date desc
    return lastMessage.createdAt;
  };

  const getLastMessageType = (messages: any[]) => {
    if (!messages || messages.length === 0) return '';
    const lastMessage = messages[0]; // Assuming messages are sorted by date desc
    return lastMessage.messageType;
  };

  const getLastMessageStatus = (messages: any[]) => {
    if (!messages || messages.length === 0) return '';
    const lastMessage = messages[0]; // Assuming messages are sorted by date desc
    return lastMessage.messageStatus;
  };

  // Load messages when room is selected
  useEffect(() => {
    const loadMessages = async () => {
      if (selectedRoom) {
        setMessagesLoading(true);
        setCurrentPage(1); // Reset pagination
        try {
          const response = await chatMessageApi.getMessages(parseInt(selectedRoom.id), {
            limit: 10,
            page: 1,
          });
          const apiMessages = response.messages || [];

          // Transform API messages to UI format
          const transformedMessages: Message[] = apiMessages.map((msg: any) => {
            // Determine message status based on read status and message status
            let status = msg.messageStatus?.toLowerCase() || 'delivered';

            // If message is from current user and has been read by others, show as read
            if (msg.userId.toString() === currentUser?.id?.toString() && msg.isRead) {
              status = 'read';
            }

            return {
              id: msg.id.toString(),
              senderId: msg.userId.toString(),
              senderName: `${msg.user.firstName} ${msg.user.lastName}`,
              content: msg.content,
              timestamp: msg.createdAt,
              type: msg.messageType.toLowerCase() as 'text' | 'image' | 'file',
              status: status as 'sending' | 'delivered' | 'read' | 'failed',
              user: {
                id: msg.user.id,
                firstName: msg.user.firstName,
                lastName: msg.user.lastName,
                imageUrl: msg.user.imageUrl || undefined,
              },
            };
          });
          setMessages(transformedMessages.reverse()); // Show oldest first

          // Check if there are more messages to load
          setHasMoreMessages(apiMessages.length === 10);
        } catch (error) {
          console.error('Failed to load messages:', error);
          setMessages([]);
          setHasMoreMessages(false);
        } finally {
          setMessagesLoading(false);
        }
      } else {
        setMessages([]);
        setMessagesLoading(false);
        setHasMoreMessages(false);
        setCurrentPage(1);
      }
    };

    loadMessages();
  }, [selectedRoom]);

  // Handle loading more messages for pagination
  const handleLoadMoreMessages = async () => {
    if (!selectedRoom || loadingMoreMessages || !hasMoreMessages) return;

    setLoadingMoreMessages(true);
    try {
      const nextPage = currentPage + 1;
      const response = await chatMessageApi.getMessages(parseInt(selectedRoom.id), {
        limit: 10,
        page: nextPage,
      });
      const apiMessages = response.messages || [];

      if (apiMessages.length > 0) {
        // Transform API messages to UI format
        const transformedMessages: Message[] = apiMessages.map((msg: any) => {
          // Determine message status based on read status and message status
          let status = msg.messageStatus?.toLowerCase() || 'delivered';

          // If message is from current user and has been read by others, show as read
          if (msg.userId.toString() === currentUser?.id?.toString() && msg.isRead) {
            status = 'read';
          }

          return {
            id: msg.id.toString(),
            senderId: msg.userId.toString(),
            senderName: `${msg.user.firstName} ${msg.user.lastName}`,
            content: msg.content,
            timestamp: msg.createdAt,
            type: msg.messageType.toLowerCase() as 'text' | 'image' | 'file',
            status: status as 'sending' | 'delivered' | 'read' | 'failed',
            user: {
              id: msg.user.id,
              firstName: msg.user.firstName,
              lastName: msg.user.lastName,
              imageUrl: msg.user.imageUrl || undefined,
            },
          };
        });

        // Prepend older messages (reverse since we want oldest first)
        // Filter out any duplicate messages to prevent key conflicts
        setMessages(prev => {
          const existingIds = new Set(prev.map(msg => msg.id));
          const newMessages = transformedMessages.filter(msg => !existingIds.has(msg.id));
          return [...newMessages.reverse(), ...prev];
        });
        setCurrentPage(nextPage);

        // Check if there are more messages to load
        setHasMoreMessages(apiMessages.length === 10);
      } else {
        setHasMoreMessages(false);
      }
    } catch (error) {
      console.error('Failed to load more messages:', error);
      setHasMoreMessages(false);
    } finally {
      setLoadingMoreMessages(false);
    }
  };

  useSocketEvent('new_message', (data: any) => {
    if (!selectedRoom) return;
    const newMessage: Message = {
      id: data.messageId.toString(),
      senderId: data.sender.id.toString(),
      senderName: `${data.sender.firstName} ${data.sender.lastName}`,
      content: data.content,
      timestamp: data.createdAt,
      type: data.messageType.toLowerCase() as 'text' | 'image' | 'file' | 'sticker',
      status: data.status?.toLowerCase() || 'delivered',
      user: {
        id: data.sender.id,
        firstName: data.sender.firstName,
        lastName: data.sender.lastName,
        imageUrl: data.sender.imageUrl || undefined,
      },
    };

    // Add message to current chat (prevent duplicates)
    setMessages(prev => {
      const existingIds = new Set(prev.map(msg => msg.id));
      if (existingIds.has(newMessage.id)) {
        return prev; // Message already exists, don't add duplicate
      }
      return [...prev, newMessage];
    });

    // Update the room's last message and unread count
    setRooms(prev => ({
      ...prev,
      [selectedRoom.type]: prev[selectedRoom.type].map(room =>
        room.id === selectedRoom.id
          ? {
              ...room,
              lastMessage: newMessage.content,
              lastMessageTime: newMessage.timestamp,
              lastMessageType: newMessage.type,
              lastMessageStatus: newMessage.status,
            }
          : room
      ),
    }));

    // Update unread counts for other rooms if this message is not in the currently selected room
    if (data.chatId && data.chatId.toString() !== selectedRoom.id) {
      setRooms(prev => {
        const updatedRooms = { ...prev };
        Object.keys(updatedRooms).forEach(roomType => {
          updatedRooms[roomType as RoomType] = updatedRooms[roomType as RoomType].map(room =>
            room.id === data.chatId.toString()
              ? {
                  ...room,
                  unreadCount: (room.unreadCount || 0) + 1,
                  lastMessage: newMessage.content,
                  lastMessageTime: newMessage.timestamp,
                  lastMessageType: newMessage.type,
                  lastMessageStatus: newMessage.status,
                }
              : room
          );
        });
        return updatedRooms;
      });
    }
  });

  const handleSendMessage = async (
    content: string,
    type: 'text' | 'file' | 'image' | 'sticker' | 'link'
  ) => {
    if (!selectedRoom) return;

    try {
      emit('send_message', {
        chatId: selectedRoom.id,
        chatType: selectedRoom.type,
        userId: currentUser?.id,
        content: content,
        messageType: type,
      });
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  // Handle message deletion
  const handleMessageDeleted = (messageId: string) => {
    // Remove the message from the UI
    setMessages(prev => prev.filter(msg => msg.id !== messageId));

    // Optionally emit socket event for real-time updates to other users
    if (selectedRoom) {
      emit('message_deleted', {
        chatId: selectedRoom.id,
        messageId: messageId,
      });
    }
  };

  const { isConnected } = useSocket();
  const emit = useSocketEmit();

  useEffect(() => {
    if (isConnected) {
      console.log(`Socket connected: ${isConnected}`);
    }
  }, [isConnected]);

  // Listen for incoming messages
  useSocketEvent('receive_join', (data: any) => {
    console.log(`Received receive_join:`, data);
  });

  useSocketEvent('receive_leave', (data: any) => {
    console.log(`Received receive_leave:`, data);
  });

  // Listen for message read status updates
  useSocketEvent('messages_read', (data: any) => {
    if (!data.chatId || !data.userId) return;

    // Only update if the read status is from another user
    if (data.userId.toString() === currentUser?.id?.toString()) return;

    // Update message read status in the UI for current chat
    if (selectedRoom && selectedRoom.id === data.chatId.toString()) {
      setMessages(prev =>
        prev.map(msg => ({
          ...msg,
          status: msg.senderId === currentUser?.id?.toString() ? 'read' : msg.status,
        }))
      );
    }

    // Update unread count for the chat in sidebar
    setRooms(prev => {
      const updatedRooms = { ...prev };
      Object.keys(updatedRooms).forEach(roomType => {
        updatedRooms[roomType as RoomType] = updatedRooms[roomType as RoomType].map(room =>
          room.id === data.chatId.toString()
            ? { ...room, unreadCount: 0 }
            : room
        );
      });
      return updatedRooms;
    });
  });

  // Listen for unread count updates
  useSocketEvent('unread_count_updated', (data: any) => {
    if (!data.chatId || data.userId?.toString() === currentUser?.id?.toString()) return;

    // Update unread count for specific chat
    setRooms(prev => {
      const updatedRooms = { ...prev };
      Object.keys(updatedRooms).forEach(roomType => {
        updatedRooms[roomType as RoomType] = updatedRooms[roomType as RoomType].map(room =>
          room.id === data.chatId.toString()
            ? { ...room, unreadCount: data.unreadCount || 0 }
            : room
        );
      });
      return updatedRooms;
    });
  });

  // Helper function to move a chat to the top of its type list
  const moveChatToTop = (chatId: string, chatType: RoomType) => {
    setRooms(prev => {
      const roomsOfType = prev[chatType];
      const chatIndex = roomsOfType.findIndex(room => room.id === chatId);

      if (chatIndex > 0) { // Only move if not already at top
        const chatToMove = roomsOfType[chatIndex];
        const updatedRooms = [
          chatToMove,
          ...roomsOfType.slice(0, chatIndex),
          ...roomsOfType.slice(chatIndex + 1)
        ];

        return {
          ...prev,
          [chatType]: updatedRooms
        };
      }

      return prev;
    });
  };

  // Helper function to create a new chat room from notification data
  const createChatFromNotification = (messageData: any, chatType: RoomType) => {
    const chatId = messageData.data.message.chatId.toString();
    const user = messageData.data.user;
    const message = messageData.data.message;

    const newRoom: Room = {
      id: chatId,
      name: getDefaultChatName({
        chatType: chatType.toUpperCase(),
        chatUsers: [{ user }]
      }, true),
      type: chatType,
      lastMessage: message.content,
      lastMessageTime: new Date().toISOString(),
      lastMessageType: message.messageType || 'TEXT',
      lastMessageStatus: message.messageStatus || 'DELIVERED',
      unreadCount: 1,
      isOnline: chatType === 'private' ? true : undefined,
      isBot: false, // Default to false for notification-created chats
      botDuration: undefined,
      chatUsers: [{ user }]
    };

    setRooms(prev => ({
      ...prev,
      [chatType]: [newRoom, ...prev[chatType]]
    }));
  };

  // Listen for chat notifications
  useSocketEvent('chat_notification', (messageData: any) => {
    console.log('New chat_notification received:', messageData);

    if (!messageData?.data?.message || !currentUser) return;

    const message = messageData.data.message;
    const user = messageData.data.user;
    const chatId = message.chatId?.toString();
    const chatType = message.chatType?.toLowerCase() as RoomType;

    if (!chatId || !chatType || !['private', 'task', 'department', 'organization'].includes(chatType)) {
      console.warn('Invalid chat notification data:', messageData);
      return;
    }

    // Skip if this is the current user's own message
    if (message.userId?.toString() === currentUser.id?.toString()) {
      return;
    }

    // Check if user is currently on the tab corresponding to the chatType
    const isOnCurrentTab = activeTab === chatType;

    if (!isOnCurrentTab) {
      // User is NOT on the corresponding tab - fetch fresh data for that chatType
      console.log(`User not on ${chatType} tab, refreshing tab data for unread count update`);
      loadChatsByType(chatType);
    } else {
      // User IS on the corresponding tab - check if chat exists in current list
      const currentRooms = rooms[chatType];
      const existingChatIndex = currentRooms.findIndex(room => room.id === chatId);

      if (existingChatIndex >= 0) {
        // Chat exists - update it and move to top
        console.log(`Updating existing chat ${chatId} and moving to top`);

        setRooms(prev => {
          const roomsOfType = [...prev[chatType]];
          const existingChat = roomsOfType[existingChatIndex];

          // Update the chat with new message data
          const updatedChat: Room = {
            ...existingChat,
            lastMessage: message.content,
            lastMessageTime: new Date().toISOString(),
            lastMessageType: message.messageType || 'TEXT',
            lastMessageStatus: message.messageStatus || 'DELIVERED',
            unreadCount: (existingChat.unreadCount || 0) + 1
          };

          // Remove from current position and add to top
          roomsOfType.splice(existingChatIndex, 1);
          roomsOfType.unshift(updatedChat);

          return {
            ...prev,
            [chatType]: roomsOfType
          };
        });
      } else {
        // Chat does NOT exist - create new chat entry at top
        console.log(`Creating new chat ${chatId} at top of list`);
        createChatFromNotification(messageData, chatType);
      }
    }
  });

  const handleRoomSelect = async (room: Room) => {
    setSelectedRoom(room);

    emit('join_chat', {
      chat_id: room.id,
    });

    // Mark as read (reset unread count)
    if (room.unreadCount && room.unreadCount > 0) {
      try {
        // Call API to mark all messages in chat as read
        await chatMessageApi.markChatAsRead(parseInt(room.id));

        // Update UI to show zero unread count
        setRooms(prev => ({
          ...prev,
          [room.type]: prev[room.type].map(r => (r.id === room.id ? { ...r, unreadCount: 0 } : r)),
        }));

        // Emit socket event to notify other clients
        emit('messages_read', {
          chatId: room.id,
          userId: currentUser?.id,
        });
      } catch (error) {
        console.error('Error marking chat as read:', error);
      }
    }
  };

  const handleCreateChat = (chatType: RoomType) => {
    setCreateChatType(chatType);
    setShowCreateModal(true);
  };

  const handleChatCreated = (newChat: any) => {
    // Add the new chat to the appropriate room list
    const chatType = newChat.chatType.toLowerCase() as RoomType;
    const room: Room = {
      id: newChat.id.toString(),
      name: newChat.name || getDefaultChatName(newChat, true),
      type: chatType,
      lastMessage: '',
      lastMessageTime: newChat.createdAt,
      unreadCount: 0,
      isOnline: chatType === 'private' ? true : undefined,
      isBot: newChat.isBot || false,
      botDuration: newChat.botDuration || undefined,
      chatUsers: newChat.chatUsers || [],
    };

    setRooms(prev => ({
      ...prev,
      [chatType]: [room, ...prev[chatType]],
    }));

    // Select the new chat
    setSelectedRoom(room);
    setActiveTab(chatType);
  };

  // Handle tab change with API call
  const handleTabChange = async (newTab: RoomType) => {
    setActiveTab(newTab);

    // Load chats for the selected tab if not already loaded
    if (currentUser && (rooms[newTab].length === 0 || shouldRefreshTab(newTab))) {
      await loadChatsByType(newTab);
    }
  };

  // Helper to determine if tab should be refreshed
  const shouldRefreshTab = (tabType: RoomType) => {
    // You can add logic here to determine if the tab data is stale
    // For now, we'll refresh if the tab has no data
    return rooms[tabType].length === 0;
  };

  // Function to manually refresh current tab
  const refreshCurrentTab = async () => {
    await loadChatsByType(activeTab);
  };

  // Function to refresh all chat data (for visibility change events)
  const refreshAllChats = async () => {
    if (currentUser) {
      await loadChats();
    }
  };

  // Refresh chat data when the page becomes visible or user navigates to it
  useEffect(() => {
    const handlePageVisibilityChange = () => {
      // Refresh when page becomes visible and user is loaded
      if (!document.hidden && currentUser) {
        refreshAllChats();
      }
    };

    // Add event listener for page visibility changes
    document.addEventListener('visibilitychange', handlePageVisibilityChange);

    // Also refresh when the component mounts (user navigates to chat page)
    if (currentUser) {
      refreshAllChats();
    }

    return () => {
      document.removeEventListener('visibilitychange', handlePageVisibilityChange);
    };
  }, [currentUser]); // Depend on currentUser to ensure we only refresh when user is loaded

  // Mobile sidebar handlers
  const handleMobileSidebarOpen = () => {
    setIsMobileSidebarOpen(true);
  };

  const handleMobileSidebarClose = () => {
    setIsMobileSidebarOpen(false);
  };

  const handleRoomSelectWithMobileClose = (room: Room) => {
    handleRoomSelect(room);
    // Close mobile sidebar when a room is selected
    setIsMobileSidebarOpen(false);
  };

  return (
    <ChatContainer>
      <ChatSidebar
        rooms={rooms}
        activeTab={activeTab}
        selectedRoom={selectedRoom}
        searchQuery={searchQuery}
        loading={tabLoading[activeTab]}
        currentUser={currentUser}
        onTabChange={handleTabChange}
        onRoomSelect={handleRoomSelectWithMobileClose}
        onSearchChange={setSearchQuery}
        onCreateChat={handleCreateChat}
        onRefreshChats={refreshAllChats}
        isMobileOpen={isMobileSidebarOpen}
        onMobileClose={handleMobileSidebarClose}
      />
      <ChatMain
        selectedRoom={selectedRoom}
        messages={messages}
        onSendMessage={handleSendMessage}
        onLoadMoreMessages={handleLoadMoreMessages}
        hasMoreMessages={hasMoreMessages}
        loadingMore={loadingMoreMessages}
        currentUser={currentUser}
        loading={messagesLoading}
        onMessageDeleted={handleMessageDeleted}
        onMobileSidebarOpen={handleMobileSidebarOpen}
      />
      <CreateChatModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onChatCreated={handleChatCreated}
      />

      {showDiagnostic && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0,0,0,0.5)',
            zIndex: 1000,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <div
            style={{
              backgroundColor: 'white',
              borderRadius: '8px',
              maxWidth: '800px',
              maxHeight: '80vh',
              overflow: 'auto',
              position: 'relative',
            }}
          >
            <button
              onClick={() => setShowDiagnostic(false)}
              style={{
                position: 'absolute',
                top: '10px',
                right: '10px',
                background: 'none',
                border: 'none',
                fontSize: '20px',
                cursor: 'pointer',
              }}
            >
              ×
            </button>
            <ChatDiagnostic onRefreshChats={() => loadChats()} />
          </div>
        </div>
      )}
    </ChatContainer>
  );
}

// Loading component for Suspense fallback
const ChatPageLoading = () => (
  <ChatContainer>
    <div
      style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}
    >
      <div>Loading chat...</div>
    </div>
  </ChatContainer>
);

export default function ChatPage() {
  return (
    <Suspense fallback={<ChatPageLoading />}>
      <ChatPageContent />
    </Suspense>
  );
}
