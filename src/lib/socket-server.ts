import { io as ioClient } from 'socket.io-client';

/**
 * Server-side socket utility for emitting events from API endpoints
 * This connects to the socket server to emit notifications for real-time updates
 */
class SocketServerService {
  private socket: ReturnType<typeof ioClient> | null = null;
  private isConnected = false;

  constructor() {
    this.initializeConnection();
  }

  private initializeConnection() {
    try {
      const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL;
      if (!socketUrl) {
        console.warn('NEXT_PUBLIC_SOCKET_URL not configured, socket emissions will be skipped');
        return;
      }

      this.socket = ioClient(socketUrl, {
        transports: ['websocket'],
        autoConnect: true,
      });

      this.socket.on('connect', () => {
        console.log('Server-side socket connected');
        this.isConnected = true;
      });

      this.socket.on('disconnect', () => {
        console.log('Server-side socket disconnected');
        this.isConnected = false;
      });

      this.socket.on('connect_error', (error: Error) => {
        console.error('Server-side socket connection error:', error);
        this.isConnected = false;
      });
    } catch (error) {
      console.error('Failed to initialize server-side socket connection:', error);
    }
  }

  /**
   * Emit a notification event for real-time updates
   * @param service - The service name (e.g., 'task', 'assistant')
   * @param id - The record ID
   * @param action - The action performed (CREATE, UPDATE, DELETE)
   */
  public emitNotification(
    service: string,
    id: number | string,
    action: 'CREATE' | 'UPDATE' | 'DELETE' = 'UPDATE'
  ) {
    if (!this.socket || !this.isConnected) {
      console.warn('Socket not connected, skipping notification emission');
      return;
    }

    try {
      this.socket.emit('send_notification', {
        service,
        id,
        action,
        timestamp: new Date().toISOString(),
      });
      console.log(`Emitted notification: ${service}:${id}:${action}`);
    } catch (error) {
      console.error('Failed to emit socket notification:', error);
    }
  }

  /**
   * Emit a specific event with custom data
   * @param eventName - The event name to emit
   * @param data - The data to send with the event
   */
  public emit(eventName: string, data: any) {
    if (!this.socket || !this.isConnected) {
      console.warn('Socket not connected, skipping event emission');
      return;
    }

    try {
      this.socket.emit(eventName, data);
      console.log(`Emitted event: ${eventName}`, data);
    } catch (error) {
      console.error(`Failed to emit socket event ${eventName}:`, error);
    }
  }

  /**
   * Check if socket is connected
   */
  public get connected(): boolean {
    return this.isConnected;
  }

  /**
   * Disconnect the socket
   */
  public disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
    }
  }
}

// Create a singleton instance
const socketServerService = new SocketServerService();

export default socketServerService;
